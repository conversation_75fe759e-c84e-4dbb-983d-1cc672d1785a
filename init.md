请帮我初始化项目宪法，参数如下：
- 项目名称：我的Web应用
- 项目描述：一个基于FastAPI的现代Web应用，提供用户管理和数据分析功能
- 技术栈：{"backend": "FastAPI", "database": "PostgreSQL", "frontend": "React", "deployment": "Docker"}
- 团队规模：8
- 团队经验：高级
- 性能要求：支持100并发用户，响应时间<200ms
- 安全要求：OAuth2认证，数据加密传输
- 设计原则：SOLID原则，微服务架构，API优先
- 约束条件：不使用ORM，必须有单元测试覆盖率>80%



```json
{
  "mcpServers": {
	  "spec-driven-development": {
      "command": "python",
      "args": ["C:\\Users\\<USER>\\demo\\mcp-spec\\spec_driven_mcp.py"],
      "cwd": "C:\\Users\\<USER>\\demo\\mcp-spec",
      "env": {
        "PYTHONPATH": "C:\\Users\\<USER>\\demo\\mcp-spec"
      }
    }
  }
}
}
```

```json
{
  "mcpServers": {
    "spec-driven-development": {
      "command": "python",
      "args": ["spec_driven_mcp.py"],
      "cwd": "C:\\Users\\<USER>\\demo\\mcp-spec",
      "env": {
        "PYTHONPATH": "."
      }
    }
  }
}
```

```json
{
 "mcpServers": {
   "spec-driven-development": {
     "url": "http://127.0.0.1:8000/sse",
     "type": "sse"
   }
 }
}
```

